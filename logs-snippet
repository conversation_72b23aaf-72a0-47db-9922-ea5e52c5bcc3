[Warning] cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation (3.4.17, line 64)
[Log] Temporary user detected with ID token (guest, line 101)
[Info] Successfully preconnected to https://fonts.gstatic.com/
[Warning] [2025-08-05T18:31:21.320Z]  @firebase/app-compat: – "↵    Warning: Firebase is already defined in the global scope. Please make sure↵    Firebase library is only loaded once.↵  " (firebase-app-compat.js, line 1)
"
    Warning: Firebase is already defined in the global scope. Please make sure
    Firebase library is only loaded once.
  "
[Log] Guest dashboard configuration: (guest, line 1209)
[Log] - User ID: – "temp_magic_" (guest, line 1210)
[Log] - Property ID: – "eb0b5b41-34fc-4408-9f6a-cca3a243ce67" (guest, line 1211)
[Log] - WebSocket URL: – "http://127.0.0.1:8080/" (guest, line 1212)
[Log] - WebSocket API URL: – "http://127.0.0.1:8080/" (guest, line 1213)
[Log] - Guest Name: – "Tester" (guest, line 1214)
[Log] - Phone Number: – "***-***-9881" (guest, line 1215)
[Log] - Secure credentials will be loaded via API endpoints (guest, line 1216)
[Log] Initialized reservations data: – [Object] (1) (guest, line 2258)
[Object]Array (1)
[Log] Loading Firebase configuration from secure endpoint... (guest, line 1132)
[Log] Checking authentication state... (auth.js, line 358)
[Log] Initializing Firebase securely for auth... (auth.js, line 10)
[Log] Firebase already loaded, skipping load from resource-loader (resource-loader.js, line 89)
[Log] === GUEST DASHBOARD INITIALIZATION === (guest_dashboard_main.js, line 600)
[Log] DOM Content Loaded at: – "2025-08-05T18:31:21.428Z" (guest_dashboard_main.js, line 601)
[Log] Initializing dashboard state from template data (guest_dashboard_utils.js, line 956)
[Log] Initialized dashboard state with reservations from template (guest_dashboard_utils.js, line 982)
[Log] Dashboard state initialized: – {propertyId: "eb0b5b41-34fc-4408-9f6a-cca3a243ce67", guestName: "Guest", phoneNumber: "***-***-9881", …} (guest_dashboard_utils.js, line 983)
{propertyId: "eb0b5b41-34fc-4408-9f6a-cca3a243ce67", guestName: "Guest", phoneNumber: "***-***-9881", reservationsCount: 1}Object
[Log] Found reservations in template data, checking for guest info: – 1 (guest_dashboard_main.js, line 614)
[Log] User ID: – "temp_magic_" (guest_dashboard_main.js, line 715)
[Log] Guest name: – "Guest" (guest_dashboard_main.js, line 716)
[Log] Phone number: – "Available" (guest_dashboard_main.js, line 717)
[Log] updateGuestNameDisplay function is properly defined (guest_dashboard_main.js, line 736)
[Log] Updating guest name display... (guest_dashboard_main.js, line 1866)
[Log] Using guest name from dashboard state: "Tester" (source: unknown) (guest_dashboard_main.js, line 1882)
[Log] Updating dashboardState.guestName from "Guest" to "Tester" (guest_dashboard_main.js, line 1889)
[Log] Waiting for Firebase to initialize securely... (guest_dashboard_main.js, line 744)
[Log] Using Gemini voice: Aoede (guest_dashboard_voice_call.js, line 129)
[Log] Using Gemini language: en-US (guest_dashboard_voice_call.js, line 130)
[Log] updateGuestName called with name=Tester, source=magic_link (guest_dashboard_utils.js, line 899)
[Log] Updating guest name to "Tester" from source: magic_link (guest_dashboard_utils.js, line 926)
[Log] Directly updated DOM element guest-name to "Tester" (guest_dashboard_utils.js, line 939)
[Log] Guest name updated successfully to "Tester" from source: magic_link (guest_dashboard_utils.js, line 948)
[Log] Initialized guest name: "Tester" from source: magic_link (guest_dashboard_main.js, line 689)
[Log] Firebase configuration loaded securely (guest, line 1152)
[Log] Firebase initialized securely (guest, line 1157)
[Log] Firebase auth initialized securely (auth.js, line 13)
[Log] Firebase initialized successfully (guest_dashboard_main.js, line 746)
[Log] Firebase already initialized, using existing instance (guest_dashboard_main.js, line 1020)
[Log] initializeSocketIOProcess called (guest_dashboard_text_chat.js, line 292)
[Log] Temporary user detected, using temporary ID token (guest_dashboard_text_chat.js, line 296)
[Log] Temporary user token available: – "Yes" (guest_dashboard_text_chat.js, line 297)
[Log] Temporary user flag: – true (guest_dashboard_text_chat.js, line 298)
[Log] Stored temporary token in dashboardState and window.storedIdToken (guest_dashboard_text_chat.js, line 304)
[Log] checkAndEnableChatButton called (guest_dashboard_text_chat.js, line 392)
[Log] - idToken: – "Present (hidden)" (guest_dashboard_text_chat.js, line 393)
[Log] - propertyId: – "eb0b5b41-34fc-4408-9f6a-cca3a243ce67" (guest_dashboard_text_chat.js, line 394)
[Log] - window.storedIdToken: – "Present (hidden)" (guest_dashboard_text_chat.js, line 395)
[Log] - window.confirmedPropertyId: – "Missing" (guest_dashboard_text_chat.js, line 396)
[Log] - window.isTemporaryUser: – true (guest_dashboard_text_chat.js, line 397)
[Log] - window.tempIdToken: – "Present (hidden)" (guest_dashboard_text_chat.js, line 398)
[Log] Both token and property ID are ready, chat can auto-start when needed (guest_dashboard_text_chat.js, line 405)
[Log] initializeChat called (guest_dashboard_text_chat.js, line 1086)
[Log] Chat UI elements found: (guest_dashboard_text_chat.js, line 1096)
[Log] - chatInput: – "Found" (guest_dashboard_text_chat.js, line 1097)
[Log] - sendMessageButton: – "Found" (guest_dashboard_text_chat.js, line 1098)
[Log] Chat connection status: – "Disconnected" (guest_dashboard_text_chat.js, line 1015)
[Log] checkAndEnableChatButton called (guest_dashboard_text_chat.js, line 392)
[Log] - idToken: – "Present (hidden)" (guest_dashboard_text_chat.js, line 393)
[Log] - propertyId: – "eb0b5b41-34fc-4408-9f6a-cca3a243ce67" (guest_dashboard_text_chat.js, line 394)
[Log] - window.storedIdToken: – "Present (hidden)" (guest_dashboard_text_chat.js, line 395)
[Log] - window.confirmedPropertyId: – "Missing" (guest_dashboard_text_chat.js, line 396)
[Log] - window.isTemporaryUser: – true (guest_dashboard_text_chat.js, line 397)
[Log] - window.tempIdToken: – "Present (hidden)" (guest_dashboard_text_chat.js, line 398)
[Log] Both token and property ID are ready, chat can auto-start when needed (guest_dashboard_text_chat.js, line 405)
[Log] Setting up speech recognition... (guest_dashboard_text_chat.js, line 1117)
[Log] Web Speech API supported and initialized. (guest_dashboard_text_chat.js, line 210)
[Log] Speech recognition enabled (guest_dashboard_text_chat.js, line 1125)
[Log] Voice selector not found in the DOM (guest_dashboard_main.js, line 880)
[Log] === CHECKING RESERVATION LOADING CONDITION === (guest_dashboard_main.js, line 933)
[Log] reservationsLoaded: – undefined (guest_dashboard_main.js, line 934)
[Log] isLoadingReservations: – undefined (guest_dashboard_main.js, line 935)
[Log] CURRENT_USER_ID: – "temp_magic_" (guest_dashboard_main.js, line 936)
[Log] First time loading reservations (guest_dashboard_main.js, line 944)
[Log] Loading reservations using imported function (guest_dashboard_main.js, line 953)
[Log] Loading reservations... (guest_dashboard_reservations.js, line 23)
[Log] Using user ID for reservation lookup: – "temp_magic_" (guest_dashboard_reservations.js, line 57)
[Log] Calling reservations API endpoint: – "/api/reservations/temp_magic_" (guest_dashboard_reservations.js, line 63)
[Log] Initializing voice call system... (guest_dashboard_voice_call.js, line 301)
[Log] Using stored language preference: – "en-US" (guest_dashboard_voice_call.js, line 321)
[Log] Voice selector not found in the DOM (guest_dashboard_voice_call.js, line 1295)
[Log] checkAndEnableChatButton called (guest_dashboard_text_chat.js, line 392)
[Log] - idToken: – "Present (hidden)" (guest_dashboard_text_chat.js, line 393)
[Log] - propertyId: – "eb0b5b41-34fc-4408-9f6a-cca3a243ce67" (guest_dashboard_text_chat.js, line 394)
[Log] - window.storedIdToken: – "Present (hidden)" (guest_dashboard_text_chat.js, line 395)
[Log] - window.confirmedPropertyId: – "Missing" (guest_dashboard_text_chat.js, line 396)
[Log] - window.isTemporaryUser: – true (guest_dashboard_text_chat.js, line 397)
[Log] - window.tempIdToken: – "Present (hidden)" (guest_dashboard_text_chat.js, line 398)
[Log] Both token and property ID are ready, chat can auto-start when needed (guest_dashboard_text_chat.js, line 405)
[Log] Guest dashboard initialization complete (guest_dashboard_main.js, line 1013)
[Log] Updating guest name display... (guest_dashboard_main.js, line 1866)
[Log] Using guest name from dashboard state: "Tester" (source: magic_link) (guest_dashboard_main.js, line 1882)
[Log] Updated language preference from user profile: en-US (guest_dashboard_voice_call.js, line 159)
[Log] Auth state changed, user: – "signed out" (auth.js, line 377)
[Log] User is signed out of Firebase. Updating UI. (auth.js, line 474)
[Log] Current pathname: – "/magic/6XnbJbjHMQfqZlr3h9I8pcRdbXzxAsmMS7Wm/guest" (auth.js, line 488)
[Warning] User signed out (client-side), but backend session may be valid. No redirect performed to avoid loop. (auth.js, line 503)
[Log] Current pathname is not recognized as login page: – "/magic/6XnbJbjHMQfqZlr3h9I8pcRdbXzxAsmMS7Wm/guest" (auth.js, line 504)
[Log] === AUTH STATE CHANGED === (guest_dashboard_main.js, line 788)
[Log] User is signed out (guest_dashboard_main.js, line 847)
[Log] Reservations API response: – {reservations: Array, success: true} (guest_dashboard_reservations.js, line 71)
{reservations: Array, success: true}Object
[Log] Setting current property index to 0 (guest_dashboard_reservations.js, line 163)
[Log] Keeping existing property ID: eb0b5b41-34fc-4408-9f6a-cca3a243ce67 (guest_dashboard_reservations.js, line 180)
[Log] Prefetching details for 1 properties (guest_dashboard_reservations.js, line 185)
[Log] Fetching property details for eb0b5b41-34fc-4408-9f6a-cca3a243ce67 from API (guest_dashboard_utils.js, line 209)
[Log] Confirmed property name: Stunning Modern Condo in Scottsdale (guest_dashboard_utils.js, line 280)
[Log] Confirmed property address: Scottsdale, Arizona, United States (guest_dashboard_utils.js, line 281)
[Log] Property details updated for eb0b5b41-34fc-4408-9f6a-cca3a243ce67 (guest_dashboard_reservations.js, line 799)
[Log] Property details updated for eb0b5b41-34fc-4408-9f6a-cca3a243ce67 (guest_dashboard_utils.js, line 375)
[Log] Rendering reservation cards: – [Object] (1) (guest_dashboard_reservations.js, line 324)
[Object]Array (1)
[Log] Guest dashboard uses modal-based reservations, skipping main container rendering. (guest_dashboard_reservations.js, line 328)
[Log] Updating selected property UI. Current index: 0, Property ID: eb0b5b41-34fc-4408-9f6a-cca3a243ce67 (guest_dashboard_reservations.js, line 639)
[Log] === CHECK VOICE CALL READINESS === (guest_dashboard_voice_call.js, line 2479)
[Log] propertyReady: true, propertyId: eb0b5b41-34fc-4408-9f6a-cca3a243ce67 (guest_dashboard_voice_call.js, line 2480)
[Log] userIdReady: true, userId: temp_magic_ (guest_dashboard_voice_call.js, line 2481)
[Log] currentCallState: idle (guest_dashboard_voice_call.js, line 2482)
[Log] Voice call is ready, enabling button (guest_dashboard_voice_call.js, line 2486)
[Log] Adding date separator for message: – "assistant" – "01:31 PM" (guest_dashboard_text_chat.js, line 187)
[Log] addDateSeparatorIfNeeded: Using stored timestamp – "2025-08-05T18:31:22.432Z" – "->" – "Tue Aug 05 2025" (guest, line 2363)
[Log] Inserting date separator: – "Today" (guest, line 2405)
[Log] Voice call button clicked, current state: – "idle" (guest_dashboard_voice_call.js, line 355)
[Log] === PROPERTY ID DEBUG INFO === (guest_dashboard_voice_call.js, line 358)
[Log] confirmedPropertyId (imported): – "eb0b5b41-34fc-4408-9f6a-cca3a243ce67" (guest_dashboard_voice_call.js, line 359)
[Log] window.PROPERTY_ID: – "eb0b5b41-34fc-4408-9f6a-cca3a243ce67" (guest_dashboard_voice_call.js, line 360)
[Log] document.body.dataset.propertyId: – "eb0b5b41-34fc-4408-9f6a-cca3a243ce67" (guest_dashboard_voice_call.js, line 361)
[Log] window.propertyDetails: – "Available" (guest_dashboard_voice_call.js, line 362)
[Log] [VoiceCallDiagnostics] Initialized for session temp-1754418684267 (voice_call_diagnostics.js, line 44)
[Log] [VoiceCall] Diagnostics system initialized successfully (guest_dashboard_voice_call.js, line 390)
[Log] Requesting ephemeral token for voice call... (guest_dashboard_voice_call.js, line 405)
[Log] Requesting ephemeral token from server... (guest_dashboard_voice_call.js, line 1382)
[Error] Error fetching ephemeral token: – SyntaxError: The string did not match the expected pattern.
SyntaxError: The string did not match the expected pattern.
	(anonymous function) (guest_dashboard_voice_call.js:1407)
[Error] Failed to fetch ephemeral token: – SyntaxError: The string did not match the expected pattern.
SyntaxError: The string did not match the expected pattern.
	(anonymous function) (guest_dashboard_voice_call.js:410)
[Log] Falling back to direct API key fetch... (guest_dashboard_voice_call.js, line 412)
[Log] Fetching Gemini API key from secure endpoint... (guest_dashboard_voice_call.js, line 1340)
[Log] Fetched Gemini API Key successfully from secure endpoint. (guest_dashboard_voice_call.js, line 1371)
[Log] Fallback API key obtained successfully (guest_dashboard_voice_call.js, line 416)
[Log] Using authentication token: – "Present (hidden)" (guest_dashboard_voice_call.js, line 427)
[Log] Final property ID for voice call: – "eb0b5b41-34fc-4408-9f6a-cca3a243ce67" (guest_dashboard_voice_call.js, line 436)
[Log] Adding date separator for message: – "assistant" – "01:31 PM" (guest_dashboard_text_chat.js, line 187)
[Log] addDateSeparatorIfNeeded: Using stored timestamp – "2025-08-05T18:31:24.266Z" – "->" – "Tue Aug 05 2025" (guest, line 2363)
[Log] addDateSeparatorIfNeeded: Previous message timestamp – "2025-08-05T18:31:22.432Z" – "->" – "Tue Aug 05 2025" (guest, line 2384)
[Log] No date separator needed for message (guest, line 2411)
[Log] [VoiceCallDiagnostics] Session initialized successfully (voice_call_diagnostics.js, line 445)
[Log] [VoiceCallDiagnostics] Started performance monitoring (voice_call_diagnostics.js, line 367)
[Log] Microphone access granted. (guest_dashboard_voice_call.js, line 441)
[Log] Starting Gemini voice call for property: – "eb0b5b41-34fc-4408-9f6a-cca3a243ce67" (guest_dashboard_voice_call.js, line 479)
[Log] Fetching knowledge items for voice call... (guest_dashboard_voice_call.js, line 493)
[Log] Fetching knowledge items for property eb0b5b41-34fc-4408-9f6a-cca3a243ce67 from API (guest_dashboard_utils.js, line 438)
[Log] Stored raw knowledge items in propertyDetails.knowledgeItems (guest_dashboard_utils.js, line 455)
[Log] Retrieved knowledge items from Firestore: – 10 – "items found" (guest_dashboard_utils.js, line 468)
[Log] Formatted knowledge items stored in window.propertyKnowledgeItems (guest_dashboard_utils.js, line 469)
[Log] Sample knowledge items: – [Object, Object, Object] (3) (guest_dashboard_utils.js, line 473)
[Object, Object, Object]Array (3)
[Log] Knowledge items fetched successfully for voice call (guest_dashboard_voice_call.js, line 495)
[Log] Using property details: – "Available" (guest_dashboard_voice_call.js, line 501)
[Log] Property name: – "Stunning Modern Condo in Scottsdale" (guest_dashboard_voice_call.js, line 502)
[Log] Property address: – "Scottsdale, Arizona, United States" (guest_dashboard_voice_call.js, line 503)
[Log] Attempting WebSocket connection to Gemini Live API with API key: wss://generativelanguage.googleapis.com/ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent?key=REDACTED_KEY&alt=json (guest_dashboard_voice_call.js, line 563)
[Log] Using model: gemini-live-2.5-flash-preview (guest_dashboard_voice_call.js, line 565)
[Log] Creating WebSocket connection... (guest_dashboard_voice_call.js, line 568)
[Log] Voice call successfully started. (guest_dashboard_voice_call.js, line 1019)
[Log] WebSocket connection established with Gemini voice API (guest_dashboard_voice_call.js, line 580)
[Log] [VoiceCallDiagnostics] Event: WEBSOCKET_CONNECTED – {url: "wss://generativelanguage.googleapis.com/ws/google.…Service.BidiGenerateContent?key=REDACTED&alt=json", model: "gemini-live-2.5-flash-preview", voice: "Aoede", …} (voice_call_diagnostics.js, line 175)
{url: "wss://generativelanguage.googleapis.com/ws/google.…Service.BidiGenerateContent?key=REDACTED&alt=json", model: "gemini-live-2.5-flash-preview", voice: "Aoede", language: "en-US"}Object
[Log] Creating voice conversation session... (guest_dashboard_voice_call.js, line 2543)
[Log] Found reservation ID from current reservation: – "3c7072b5-043d-4514-a604-ff56a8a34aab" (guest_dashboard_voice_call.js, line 2552)
[Log] Phone number for voice conversation: – "***-***-9881" (guest_dashboard_voice_call.js, line 2569)
[Log] Including reservation ID in voice conversation: – "3c7072b5-043d-4514-a604-ff56a8a34aab" (guest_dashboard_voice_call.js, line 2581)
[Log] Including phone number in voice conversation: – "***-***-9881" (guest_dashboard_voice_call.js, line 2587)
[Log] [VOICE DEBUG] Current guest name: – "Tester" (guest_dashboard_voice_call.js, line 1418)
[Log] [VOICE DEBUG] createSharedSystemPrompt available: – "function" (guest_dashboard_voice_call.js, line 1419)
[Log] [VOICE DEBUG] Using shared system prompt function for voice call (guest_dashboard_voice_call.js, line 1423)
[Log] [SHARED PROMPT DEBUG] Creating shared system prompt for property: – "Stunning Modern Condo in Scottsdale" (guest_dashboard_utils.js, line 571)
[Log] [SHARED PROMPT DEBUG] Property details available: – "Yes" (guest_dashboard_utils.js, line 572)
[Log] [SHARED PROMPT DEBUG] Knowledge items available: – "Yes" (guest_dashboard_utils.js, line 573)
[Log] [SHARED PROMPT DEBUG] Using guest name: – "Tester" (guest_dashboard_utils.js, line 574)
[Log] [SHARED PROMPT DEBUG] globalDashboardState.guestName: – "Tester" (guest_dashboard_utils.js, line 575)
[Log] [SHARED PROMPT DEBUG] window.GUEST_NAME: – "Tester" (guest_dashboard_utils.js, line 576)
[Log] [SHARED PROMPT DEBUG] Guest name is 'Guest': – false (guest_dashboard_utils.js, line 577)
[Log] [SHARED PROMPT DEBUG] Will include name asking instruction: – false (guest_dashboard_utils.js, line 578)
[Log] createSharedSystemPrompt: propertyDetails available: – {propertyId: "eb0b5b41-34fc-4408-9f6a-cca3a243ce67", name: "Stunning Modern Condo in Scottsdale", address: "Scottsdale, Arizona, United States", …} (guest_dashboard_utils.js, line 582)
{propertyId: "eb0b5b41-34fc-4408-9f6a-cca3a243ce67", name: "Stunning Modern Condo in Scottsdale", address: "Scottsdale, Arizona, United States", checkInTime: "15:00", checkOutTime: "11:00", …}Object
[Log] createSharedSystemPrompt: Using knowledge items from globalDashboardState, count: – 10 (guest_dashboard_utils.js, line 591)
[Log] Added reservation context to system prompt: – "↵↵Reservation Details:↵Check-in: Aug 13, 2025↵Check-out: Aug 19, 2025↵" (guest_dashboard_utils.js, line 719)
"

Reservation Details:
Check-in: Aug 13, 2025
Check-out: Aug 19, 2025
"
[Log] createSharedSystemPrompt: Created system prompt with length: – 5550 (guest_dashboard_utils.js, line 787)
[Log] [VOICE DEBUG] Generated prompt length: – 5550 (guest_dashboard_voice_call.js, line 1425)
[Log] [VOICE DEBUG] Prompt contains guest name handling: – false (guest_dashboard_voice_call.js, line 1426)
[Log] Sending initial configuration to Gemini voice API (guest_dashboard_voice_call.js, line 660)
[Log] Using language: en-US for voice call (guest_dashboard_voice_call.js, line 661)
[Log] [VoiceCallDiagnostics] Event: CALL_STARTED – {model: "gemini-live-2.5-flash-preview", voice: "Aoede", language: "en-US", …} (voice_call_diagnostics.js, line 175)
{model: "gemini-live-2.5-flash-preview", voice: "Aoede", language: "en-US", websocket_url: "wss://generativelanguage.googleapis.com/ws/google.…Service.BidiGenerateContent?key=REDACTED&alt=json"}Object
[Log] 🔊 Started noise monitoring (guest_dashboard_voice_call.js, line 2058)
[Error] Error creating voice conversation session: – SyntaxError: The string did not match the expected pattern. — guest_dashboard_voice_call.js:2600
SyntaxError: The string did not match the expected pattern. — guest_dashboard_voice_call.js:2600
	(anonymous function) (guest_dashboard_voice_call.js:2612)
[Warning] Failed to create voice conversation session, transcriptions won't be stored (guest_dashboard_voice_call.js, line 599)
[Log] Adding date separator for message: – "assistant" – "01:31 PM" (guest_dashboard_text_chat.js, line 187)
[Log] addDateSeparatorIfNeeded: Using stored timestamp – "2025-08-05T18:31:27.179Z" – "->" – "Tue Aug 05 2025" (guest, line 2363)
[Log] addDateSeparatorIfNeeded: Previous message timestamp – "2025-08-05T18:31:24.266Z" – "->" – "Tue Aug 05 2025" (guest, line 2384)
[Log] No date separator needed for message (guest, line 2411)
[Log] Adding date separator for message: – "assistant" – "01:31 PM" (guest_dashboard_text_chat.js, line 187)
[Log] addDateSeparatorIfNeeded: Using stored timestamp – "2025-08-05T18:31:27.180Z" – "->" – "Tue Aug 05 2025" (guest, line 2363)
[Log] addDateSeparatorIfNeeded: Previous message timestamp – "2025-08-05T18:31:27.179Z" – "->" – "Tue Aug 05 2025" (guest, line 2384)
[Log] No date separator needed for message (guest, line 2411)
[Log] Received Blob data from Gemini (size: 26) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {setupComplete: {}} (guest_dashboard_voice_call.js, line 776)
[Log] Gemini setup complete: – {} (guest_dashboard_voice_call.js, line 2291)
[Log] Received Blob data from Gemini (size: 36) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {sessionResumptionUpdate: {}} (guest_dashboard_voice_call.js, line 776)
[Log] Sent 8192 bytes of audio to Gemini (guest_dashboard_voice_call.js, line 1006)
[Log] [GREETING DEBUG] Guest name: 'Tester', isGeneric: false, language: en-US (guest_dashboard_voice_call.js, line 103)
[Log] [GREETING DEBUG] Selected greeting: 'Hello, my name is Tester. Please greet me by name very briefly!' (guest_dashboard_voice_call.js, line 104)
[Log] Sending initial greeting message (new session) (guest_dashboard_voice_call.js, line 677)
[Log] [GREETING DEBUG] Guest name: 'Tester', isGeneric: false, language: en-US (guest_dashboard_voice_call.js, line 103)
[Log] [GREETING DEBUG] Selected greeting: 'Hello, my name is Tester. Please greet me by name very briefly!' (guest_dashboard_voice_call.js, line 104)
[Log] Greeting message: Hello, my name is Tester. Please greet me by name very briefly! (guest_dashboard_voice_call.js, line 678)
[Log] 🔊 Noise analysis: avg=0.0009, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] Received Blob data from Gemini (size: 13008) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Warning] Warning: Converted base64 data contains all zeros in the first 1000 bytes (guest_dashboard_voice_call.js, line 1614)
[Log] Created audio context with sample rate: 24000Hz (guest_dashboard_voice_call.js, line 1667)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "Hel"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "lo "}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"lo \"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "Tes"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ter"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration) (guest_dashboard_voice_call.js, line 1646)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: ", h"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\", h\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ow "}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"ow \"" (guest_dashboard_voice_call.js, line 2432)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "may"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " I "}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\" I \"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "hel"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"hel\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 90) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "p y"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 60) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ou today?"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Parsed JSON from Blob: – {serverContent: {generationComplete: true}} (guest_dashboard_voice_call.js, line 776)
[Log] Audio chunk playback complete (guest_dashboard_voice_call.js, line 1794, x2)
[Log] 🔊 Noise analysis: avg=0.0007, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] Received Blob data from Gemini (size: 452) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {turnComplete: true}, usageMetadata: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: {turnComplete: true}, usageMetadata: Object}Object
[Log] Model turn complete (guest_dashboard_voice_call.js, line 2314)
[Log] Adding date separator for message: – "assistant" – "01:31 PM" (guest_dashboard_text_chat.js, line 187)
[Log] addDateSeparatorIfNeeded: Using stored timestamp – "2025-08-05T18:31:31.696Z" – "->" – "Tue Aug 05 2025" (guest, line 2363)
[Log] addDateSeparatorIfNeeded: Previous message timestamp – "2025-08-05T18:31:27.180Z" – "->" – "Tue Aug 05 2025" (guest, line 2384)
[Log] No date separator needed for message (guest, line 2411)
[Log] Received Blob data from Gemini (size: 138) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 36) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {sessionResumptionUpdate: {newHandle: "Cih6b3g5ZHF3cjFrNHAxbGFzeXQwaTFvcTRmOTFqYmNvcWk2a2hteXZx", resumable: true}} (guest_dashboard_voice_call.js, line 776)
[Log] Received session resumption handle: – "Cih6b3g5ZHF3cjFrNHAxbGFzeXQwaTFvcTRmOTFqYmNvcWk2a2hteXZx" (guest_dashboard_voice_call.js, line 2309)
[Log] Parsed JSON from Blob: – {sessionResumptionUpdate: {}} (guest_dashboard_voice_call.js, line 776)
[Log] 🔊 Noise analysis: avg=0.0070, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] Sent 8192 bytes of audio to Gemini (guest_dashboard_voice_call.js, line 1006, x2)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: " Hey"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (user): – "\" Hey\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 81) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: ","}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 AI Voice Complete: – "Hello Tester, how may I help you today?" (guest_dashboard_voice_call.js, line 2717)
[Warning] No active voice conversation session, cannot store message (guest_dashboard_voice_call.js, line 2622)
[Log] 🎯 Displayed assistant voice transcription in chat UI (guest_dashboard_voice_call.js, line 2696)
[Log] Received Blob data from Gemini (size: 85) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: " tell"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (user): – "\" tell\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Adding date separator for message: – "assistant" – "01:31 PM" (guest_dashboard_text_chat.js, line 187)
[Log] addDateSeparatorIfNeeded: Using stored timestamp – "2025-08-05T18:31:34.614Z" – "->" – "Tue Aug 05 2025" (guest, line 2363)
[Log] addDateSeparatorIfNeeded: Previous message timestamp – "2025-08-05T18:31:31.696Z" – "->" – "Tue Aug 05 2025" (guest, line 2384)
[Log] No date separator needed for message (guest, line 2411)
[Log] Received Blob data from Gemini (size: 83) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: " me"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 86) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: " about"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 🔊 Noise analysis: avg=0.0245, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] Received Blob data from Gemini (size: 85) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: " this"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: " pro"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 85) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: "perty"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 89) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: " briefly."}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 13008) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "Tes"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ter"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: ", t"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\", t\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration) (guest_dashboard_voice_call.js, line 1646)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "his"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"his\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration) (guest_dashboard_voice_call.js, line 1646)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " is"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration) (guest_dashboard_voice_call.js, line 1646)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " a "}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration) (guest_dashboard_voice_call.js, line 1646)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "stu"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Converted base64 to ArrayBuffer: 11520 bytes (guest_dashboard_voice_call.js, line 1619)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "nni"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ng "}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"ng \"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "mod"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ern"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " co"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ndo"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " in"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Converting base64 to ArrayBuffer (length: 15360) (guest_dashboard_voice_call.js, line 1592)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " Sc"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\" Sc\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 13008) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ott"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 13008) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "sda"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"sda\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "le,"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"le,\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " Ar"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\" Ar\"" (guest_dashboard_voice_call.js, line 2432)
[Log] 🔊 Noise analysis: avg=0.0074, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "izo"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "na."}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " It"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " of"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\" of\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "fer"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "s e"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration) (guest_dashboard_voice_call.js, line 1646)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "xpa"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "nsi"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ve "}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration) (guest_dashboard_voice_call.js, line 1646)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "des"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"des\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ert"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"ert\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration) (guest_dashboard_voice_call.js, line 1646)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " vi"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ews"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " fr"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\" fr\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "om "}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "a p"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "riv"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration) (guest_dashboard_voice_call.js, line 1646)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ate"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " pa"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Received Blob data from Gemini (size: 5328) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 82) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "tio"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Queueing audio for playback (3840 bytes, ~1920 samples, ~0.08s duration) (guest_dashboard_voice_call.js, line 1646)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " "}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 112) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "and is located in a great area."}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 60) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {generationComplete: true}} (guest_dashboard_voice_call.js, line 776)
[Log] Scheduled audio chunk (0.240s) to play at 14.074, queue length: 10 (guest_dashboard_voice_call.js, line 1773)
[Log] Scheduled audio chunk (0.240s) to play at 15.994, queue length: 2 (guest_dashboard_voice_call.js, line 1773)
[Log] Sent 8192 bytes of audio to Gemini (guest_dashboard_voice_call.js, line 1006)
[Log] Audio chunk playback complete (guest_dashboard_voice_call.js, line 1794)
[Log] 🔊 Noise analysis: avg=0.0019, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] 📝 User Voice Complete: – "Hey, tell me about this property briefly." (guest_dashboard_voice_call.js, line 2717)
[Warning] No active voice conversation session, cannot store message (guest_dashboard_voice_call.js, line 2622)
[Log] displayChatMessage: Using guest name for user message: – "Tester" – "(window.GUEST_NAME =" – "Tester" – ")" (guest_dashboard_text_chat.js, line 133)
[Log] 🎯 Displayed user voice transcription in chat UI (guest_dashboard_voice_call.js, line 2696)
[Log] Adding date separator for message: – "user" – "01:31 PM" (guest_dashboard_text_chat.js, line 187)
[Log] addDateSeparatorIfNeeded: Using stored timestamp – "2025-08-05T18:31:40.849Z" – "->" – "Tue Aug 05 2025" (guest, line 2363)
[Log] addDateSeparatorIfNeeded: Previous message timestamp – "2025-08-05T18:31:34.614Z" – "->" – "Tue Aug 05 2025" (guest, line 2384)
[Log] No date separator needed for message (guest, line 2411)
[Log] 🔊 Noise analysis: avg=0.0006, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] 📝 AI Voice Complete: – "Tester, this is a stunning modern condo in Scottsdale, Arizona. It offers expansive desert views from a private patio and is located in …" (guest_dashboard_voice_call.js, line 2717)
"Tester, this is a stunning modern condo in Scottsdale, Arizona. It offers expansive desert views from a private patio and is located in a great area."
[Warning] No active voice conversation session, cannot store message (guest_dashboard_voice_call.js, line 2622)
[Log] 🎯 Displayed assistant voice transcription in chat UI (guest_dashboard_voice_call.js, line 2696)
[Log] Adding date separator for message: – "assistant" – "01:31 PM" (guest_dashboard_text_chat.js, line 187)
[Log] addDateSeparatorIfNeeded: Using stored timestamp – "2025-08-05T18:31:42.769Z" – "->" – "Tue Aug 05 2025" (guest, line 2363)
[Log] addDateSeparatorIfNeeded: Previous message timestamp – "2025-08-05T18:31:40.849Z" – "->" – "Tue Aug 05 2025" (guest, line 2384)
[Log] No date separator needed for message (guest, line 2411)
[Log] 🔊 Noise analysis: avg=0.0006, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] Audio chunk playback complete (guest_dashboard_voice_call.js, line 1794, x2)
[Log] 🔊 Noise analysis: avg=0.0005, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] Audio chunk playback complete (guest_dashboard_voice_call.js, line 1794)
[Log] Received Blob data from Gemini (size: 452) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {turnComplete: true}, usageMetadata: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: {turnComplete: true}, usageMetadata: Object}Object
[Log] Model turn complete (guest_dashboard_voice_call.js, line 2314)
[Log] Adding date separator for message: – "assistant" – "01:31 PM" (guest_dashboard_text_chat.js, line 187)
[Log] addDateSeparatorIfNeeded: Using stored timestamp – "2025-08-05T18:31:45.867Z" – "->" – "Tue Aug 05 2025" (guest, line 2363)
[Log] addDateSeparatorIfNeeded: Previous message timestamp – "2025-08-05T18:31:42.769Z" – "->" – "Tue Aug 05 2025" (guest, line 2384)
[Log] No date separator needed for message (guest, line 2411)
[Log] Received Blob data from Gemini (size: 138) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 36) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {sessionResumptionUpdate: {newHandle: "CigzZGlobWRzdmJtYjYzaXhuZTZkZ2c5amFsbDV5ZnNqcWNjYjJiODNz", resumable: true}} (guest_dashboard_voice_call.js, line 776)
[Log] Received session resumption handle: – "CigzZGlobWRzdmJtYjYzaXhuZTZkZ2c5amFsbDV5ZnNqcWNjYjJiODNz" (guest_dashboard_voice_call.js, line 2309)
[Log] Parsed JSON from Blob: – {sessionResumptionUpdate: {}} (guest_dashboard_voice_call.js, line 776)
[Log] 🔊 Noise analysis: avg=0.0005, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] Sent 8192 bytes of audio to Gemini (guest_dashboard_voice_call.js, line 1006)
[Log] 🔊 Noise analysis: avg=0.0217, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] Received Blob data from Gemini (size: 85) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: " Okay"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 81) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: ","}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 83) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: " um"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 81) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: ","}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (user): – "\",\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: " are"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 88) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: " there a"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 🔊 Noise analysis: avg=0.0274, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] Received Blob data from Gemini (size: 82) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: "ny"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 83) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: " ru"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 83) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: "les"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (user): – "\"les\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 83) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: " to"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (user): – "\" to\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 87) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: " follow"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: " for"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 🔊 Noise analysis: avg=0.0160, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: " sta"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: "ying"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 86) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {inputTranscription: {text: " here?"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (user): – "\" here?\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 13008) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "Yes"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"Yes\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: ", T"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "est"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "er,"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " th"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Converted base64 to ArrayBuffer: 11520 bytes (guest_dashboard_voice_call.js, line 1619)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ere"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " ar"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "e a"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"e a\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " co"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\" co\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "upl"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"upl\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "e o"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "f r"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"f r\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ule"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 2768) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 82) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "s t"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "o"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 13008) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " ke"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ep "}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "in "}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "min"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "d. "}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration) (guest_dashboard_voice_call.js, line 1646)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "The"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"The\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "re "}}} (guest_dashboard_voice_call.js, line 776)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "is "}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"is \"" (guest_dashboard_voice_call.js, line 2432)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "no "}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"no \"" (guest_dashboard_voice_call.js, line 2432)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "smo"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"smo\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "kin"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "g a"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "llo"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "wed"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " an"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ywh"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"ywh\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Converted base64 to ArrayBuffer: 11520 bytes (guest_dashboard_voice_call.js, line 1619)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ere"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Scheduled audio chunk (0.240s) to play at 26.981, queue length: 21 (guest_dashboard_voice_call.js, line 1773)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " on"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " th"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "e p"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "rop"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"rop\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ert"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\"ert\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "y, "}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "and"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 🔊 Noise analysis: avg=0.0006, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " we"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration) (guest_dashboard_voice_call.js, line 1646)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " as"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "k t"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration) (guest_dashboard_voice_call.js, line 1646)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "hat"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Converting base64 to ArrayBuffer (length: 15360) (guest_dashboard_voice_call.js, line 1592)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " yo"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "u k"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "eep"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " no"}}} (guest_dashboard_voice_call.js, line 776)
[Log] 📝 Fragment (ai): – "\" no\"" (guest_dashboard_voice_call.js, line 2432)
[Log] Received Blob data from Gemini (size: 15568) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 84) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration) (guest_dashboard_voice_call.js, line 1646)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "ise"}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 5328) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 82) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: Object}Object
[Log] Found inlineData in serverContent.modelTurn.parts (guest_dashboard_voice_call.js, line 2102)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: " "}}} (guest_dashboard_voice_call.js, line 776)
[Log] Received Blob data from Gemini (size: 117) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 60) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {outputTranscription: {text: "to a minimum between 11 PM and 7 AM."}}} (guest_dashboard_voice_call.js, line 776)
[Log] Parsed JSON from Blob: – {serverContent: {generationComplete: true}} (guest_dashboard_voice_call.js, line 776)
[Log] Audio chunk playback complete (guest_dashboard_voice_call.js, line 1794)
[Log] Sent 8192 bytes of audio to Gemini (guest_dashboard_voice_call.js, line 1006)
[Log] Scheduled audio chunk (0.240s) to play at 34.421, queue length: 6 (guest_dashboard_voice_call.js, line 1773)
[Log] 🔊 Noise analysis: avg=0.0006, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] Audio chunk playback complete (guest_dashboard_voice_call.js, line 1794)
[Log] Sent 8192 bytes of audio to Gemini (guest_dashboard_voice_call.js, line 1006)
[Log] 📝 User Voice Complete: – "Okay, um, are there any rules to follow for staying here?" (guest_dashboard_voice_call.js, line 2717)
[Warning] No active voice conversation session, cannot store message (guest_dashboard_voice_call.js, line 2622)
[Log] displayChatMessage: Using guest name for user message: – "Tester" – "(window.GUEST_NAME =" – "Tester" – ")" (guest_dashboard_text_chat.js, line 133)
[Log] 🎯 Displayed user voice transcription in chat UI (guest_dashboard_voice_call.js, line 2696)
[Log] Adding date separator for message: – "user" – "01:31 PM" (guest_dashboard_text_chat.js, line 187)
[Log] addDateSeparatorIfNeeded: Using stored timestamp – "2025-08-05T18:31:58.395Z" – "->" – "Tue Aug 05 2025" (guest, line 2363)
[Log] addDateSeparatorIfNeeded: Previous message timestamp – "2025-08-05T18:31:45.867Z" – "->" – "Tue Aug 05 2025" (guest, line 2384)
[Log] No date separator needed for message (guest, line 2411)
[Log] 🔊 Noise analysis: avg=0.0006, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] 📝 AI Voice Complete: – "Yes, Tester, there are a couple of rules to keep in mind. There is no smoking allowed anywhere on the property, and we ask that you kee…" (guest_dashboard_voice_call.js, line 2717)
"Yes, Tester, there are a couple of rules to keep in mind. There is no smoking allowed anywhere on the property, and we ask that you keep noise to a minimum between 11 PM and 7 AM."
[Warning] No active voice conversation session, cannot store message (guest_dashboard_voice_call.js, line 2622)
[Log] 🎯 Displayed assistant voice transcription in chat UI (guest_dashboard_voice_call.js, line 2696)
[Log] Adding date separator for message: – "assistant" – "01:32 PM" (guest_dashboard_text_chat.js, line 187)
[Log] addDateSeparatorIfNeeded: Using stored timestamp – "2025-08-05T18:32:00.418Z" – "->" – "Tue Aug 05 2025" (guest, line 2363)
[Log] addDateSeparatorIfNeeded: Previous message timestamp – "2025-08-05T18:31:58.395Z" – "->" – "Tue Aug 05 2025" (guest, line 2384)
[Log] No date separator needed for message (guest, line 2411)
[Log] 🔊 Noise analysis: avg=0.0006, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] Sent 8192 bytes of audio to Gemini (guest_dashboard_voice_call.js, line 1006)
[Log] Audio chunk playback complete (guest_dashboard_voice_call.js, line 1794)
[Log] 🔊 Noise analysis: avg=0.0005, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] 🔊 Noise analysis: avg=0.0006, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] Received Blob data from Gemini (size: 452) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {serverContent: {turnComplete: true}, usageMetadata: Object} (guest_dashboard_voice_call.js, line 776)
{serverContent: {turnComplete: true}, usageMetadata: Object}Object
[Log] Model turn complete (guest_dashboard_voice_call.js, line 2314)
[Log] Adding date separator for message: – "assistant" – "01:32 PM" (guest_dashboard_text_chat.js, line 187)
[Log] addDateSeparatorIfNeeded: Using stored timestamp – "2025-08-05T18:32:05.261Z" – "->" – "Tue Aug 05 2025" (guest, line 2363)
[Log] addDateSeparatorIfNeeded: Previous message timestamp – "2025-08-05T18:32:00.418Z" – "->" – "Tue Aug 05 2025" (guest, line 2384)
[Log] No date separator needed for message (guest, line 2411)
[Log] Received Blob data from Gemini (size: 138) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Received Blob data from Gemini (size: 36) (guest_dashboard_voice_call.js, line 762)
[Log] Blob MIME type: unknown (guest_dashboard_voice_call.js, line 763)
[Log] Parsed JSON from Blob: – {sessionResumptionUpdate: {newHandle: "CihvY2xwc3puZXB6Z29uY3J2c3AxZ2p0cGtmdHpzYXozbWVqbTE2eHA3", resumable: true}} (guest_dashboard_voice_call.js, line 776)
[Log] Received session resumption handle: – "CihvY2xwc3puZXB6Z29uY3J2c3AxZ2p0cGtmdHpzYXozbWVqbTE2eHA3" (guest_dashboard_voice_call.js, line 2309)
[Log] Parsed JSON from Blob: – {sessionResumptionUpdate: {}} (guest_dashboard_voice_call.js, line 776)
[Log] Sent 8192 bytes of audio to Gemini (guest_dashboard_voice_call.js, line 1006)
[Log] 🔊 Noise analysis: avg=0.0009, threshold=0.15, noisy=false (guest_dashboard_voice_call.js, line 1911)
[Log] Voice call button clicked, current state: – "active" (guest_dashboard_voice_call.js, line 355)
[Log] === PROPERTY ID DEBUG INFO === (guest_dashboard_voice_call.js, line 358)
[Log] confirmedPropertyId (imported): – "eb0b5b41-34fc-4408-9f6a-cca3a243ce67" (guest_dashboard_voice_call.js, line 359)
[Log] window.PROPERTY_ID: – "eb0b5b41-34fc-4408-9f6a-cca3a243ce67" (guest_dashboard_voice_call.js, line 360)
[Log] document.body.dataset.propertyId: – "eb0b5b41-34fc-4408-9f6a-cca3a243ce67" (guest_dashboard_voice_call.js, line 361)
[Log] window.propertyDetails: – "Available" (guest_dashboard_voice_call.js, line 362)
[Log] User initiated call end. (guest_dashboard_voice_call.js, line 472)
[Log] Stopping voice call. Reason: – "User ended call" (guest_dashboard_voice_call.js, line 1029)
[Log] [VoiceCallDiagnostics] Event: CALL_ENDING – {reason: "User ended call", call_state: "active", session_duration: 43949} (voice_call_diagnostics.js, line 175)
[Log] Microphone stream stopped. (guest_dashboard_voice_call.js, line 1076)
[Log] 🔇 Stopping all audio playback immediately (guest_dashboard_voice_call.js, line 1186)
[Log] ✅ All audio playback stopped successfully (guest_dashboard_voice_call.js, line 1240)
[Log] 🔇 Stopped noise monitoring and reset state (guest_dashboard_voice_call.js, line 2074)
[Log] [VoiceCallDiagnostics] Stopped performance monitoring (voice_call_diagnostics.js, line 376)
[Log] Adding date separator for message: – "assistant" – "01:32 PM" (guest_dashboard_text_chat.js, line 187)
[Log] addDateSeparatorIfNeeded: Using stored timestamp – "2025-08-05T18:32:08.220Z" – "->" – "Tue Aug 05 2025" (guest, line 2363)
[Log] addDateSeparatorIfNeeded: Previous message timestamp – "2025-08-05T18:32:05.261Z" – "->" – "Tue Aug 05 2025" (guest, line 2384)
[Log] No date separator needed for message (guest, line 2411)
[Log] WebSocket closed. Code: 1000, Reason:  (guest_dashboard_voice_call.js, line 826)
[Log] [VoiceCallDiagnostics] Session finalized with reason: User ended call (voice_call_diagnostics.js, line 483)