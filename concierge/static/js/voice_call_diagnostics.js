/*
 * Voice Call Diagnostics System
 * Comprehensive logging and monitoring for voice call quality and issues
 */

// Voice Call Diagnostics Class
class VoiceCallDiagnostics {
    constructor(sessionId, propertyId, userId) {
        this.sessionId = sessionId;
        this.propertyId = propertyId;
        this.userId = userId;
        this.startTime = Date.now();
        this.lastMetricTime = this.startTime;

        // Rate limiting for API calls
        this.lastEventTime = 0;
        this.eventQueue = [];
        this.isProcessingQueue = false;
        
        // Quality metrics tracking
        this.metrics = {
            connectionLatency: [],
            audioDropouts: 0,
            transcriptionErrors: 0,
            interruptionCount: 0,
            reconnectionCount: 0,
            averageResponseTime: [],
            audioQualityIssues: [],
            webSocketEvents: [],
            memoryUsage: [],
            bufferUnderruns: 0,
            bufferOverruns: 0
        };
        
        // Error and event tracking
        this.errors = [];
        this.warnings = [];
        this.eventTimeline = [];
        
        // Performance monitoring
        this.performanceObserver = null;
        this.memoryMonitorInterval = null;
        
        console.log(`[VoiceCallDiagnostics] Initialized for session ${sessionId}`);
    }
    
    // === CLIENT DIAGNOSTICS COLLECTION ===
    
    static collectClientDiagnostics() {
        const diagnostics = {
            // Browser Information
            userAgent: navigator.userAgent,
            browserName: VoiceCallDiagnostics.getBrowserName(),
            browserVersion: VoiceCallDiagnostics.getBrowserVersion(),
            platform: navigator.platform,
            language: navigator.language,
            languages: navigator.languages,
            
            // Device Capabilities
            mediaDevices: {
                supported: !!navigator.mediaDevices,
                enumerateDevices: !!navigator.mediaDevices?.enumerateDevices,
                getUserMedia: !!navigator.mediaDevices?.getUserMedia
            },
            
            // Audio Context Support
            audioContext: {
                supported: !!(window.AudioContext || window.webkitAudioContext),
                sampleRate: null, // Will be set after creation
                state: null,
                maxChannelCount: null
            },
            
            // WebSocket Support
            webSocket: {
                supported: !!window.WebSocket,
                extensions: null // Will be populated after connection
            },
            
            // Network Information (if available)
            connection: navigator.connection ? {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt,
                saveData: navigator.connection.saveData
            } : null,
            
            // Screen Information
            screen: {
                width: screen.width,
                height: screen.height,
                colorDepth: screen.colorDepth,
                pixelDepth: screen.pixelDepth
            },
            
            // Memory Information (if available)
            memory: navigator.deviceMemory || null,
            
            // Hardware Concurrency
            hardwareConcurrency: navigator.hardwareConcurrency || null,
            
            // Timezone
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            
            // Timestamp
            timestamp: new Date().toISOString()
        };
        
        return diagnostics;
    }
    
    static getBrowserName() {
        const userAgent = navigator.userAgent;
        if (userAgent.includes('Chrome')) return 'Chrome';
        if (userAgent.includes('Firefox')) return 'Firefox';
        if (userAgent.includes('Safari')) return 'Safari';
        if (userAgent.includes('Edge')) return 'Edge';
        if (userAgent.includes('Opera')) return 'Opera';
        return 'Unknown';
    }
    
    static getBrowserVersion() {
        const userAgent = navigator.userAgent;
        const match = userAgent.match(/(Chrome|Firefox|Safari|Edge|Opera)\/(\d+)/);
        return match ? match[2] : 'Unknown';
    }
    
    static async assessNetworkQuality() {
        return new Promise((resolve) => {
            const startTime = performance.now();
            const testImage = new Image();
            
            testImage.onload = () => {
                const loadTime = performance.now() - startTime;
                resolve({
                    latency: loadTime,
                    timestamp: new Date().toISOString(),
                    connectionType: navigator.connection?.effectiveType || 'unknown'
                });
            };
            
            testImage.onerror = () => {
                resolve({
                    latency: -1,
                    error: 'Network test failed',
                    timestamp: new Date().toISOString()
                });
            };
            
            // Use a small test resource from the same domain
            testImage.src = '/favicon.ico?' + Date.now();
        });
    }
    
    // === EVENT LOGGING ===
    
    async logEvent(eventType, details = {}, errorInfo = null, warningInfo = null) {
        const timestamp = new Date().toISOString();

        // Add to local timeline
        this.eventTimeline.push({
            timestamp,
            event: eventType,
            details
        });

        // Track errors and warnings locally
        if (errorInfo) {
            this.errors.push({ timestamp, event: eventType, error: errorInfo });
        }
        if (warningInfo) {
            this.warnings.push({ timestamp, event: eventType, warning: warningInfo });
        }

        console.log(`[VoiceCallDiagnostics] Event: ${eventType}`, details);

        // Rate limit API calls - only send important events immediately
        const importantEvents = ['SESSION_INITIALIZED', 'CALL_STARTED', 'CALL_ENDING', 'WEBSOCKET_ERROR', 'MICROPHONE_ACCESS_DENIED'];

        if (importantEvents.includes(eventType)) {
            // Send important events immediately
            this.sendEventToBackend(eventType, details, errorInfo, warningInfo);
        } else {
            // Queue less important events and send in batches
            this.eventQueue.push({
                event_type: eventType,
                details,
                error_info: errorInfo,
                warning_info: warningInfo,
                timestamp
            });

            // Process queue every 10 seconds
            if (!this.isProcessingQueue) {
                setTimeout(() => this.processEventQueue(), 10000);
                this.isProcessingQueue = true;
            }
        }
    }

    async sendEventToBackend(eventType, details, errorInfo, warningInfo) {
        try {
            await fetch('/api/voice-call/event', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    event_type: eventType,
                    details,
                    error_info: errorInfo,
                    warning_info: warningInfo
                })
            });
        } catch (error) {
            console.error(`[VoiceCallDiagnostics] Failed to log event ${eventType}:`, error);
        }
    }

    async processEventQueue() {
        if (this.eventQueue.length === 0) {
            this.isProcessingQueue = false;
            return;
        }

        // Send up to 5 events at once
        const eventsToSend = this.eventQueue.splice(0, 5);

        for (const event of eventsToSend) {
            await this.sendEventToBackend(
                event.event_type,
                event.details,
                event.error_info,
                event.warning_info
            );

            // Small delay between events
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // Continue processing if there are more events
        if (this.eventQueue.length > 0) {
            setTimeout(() => this.processEventQueue(), 5000);
        } else {
            this.isProcessingQueue = false;
        }
    }
    
    // === QUALITY MONITORING ===
    
    assessAudioQuality(audioBuffer) {
        if (!audioBuffer || audioBuffer.byteLength === 0) {
            return null;
        }
        
        const analysis = {
            timestamp: Date.now(),
            bufferSize: audioBuffer.byteLength,
            silenceRatio: this.calculateSilenceRatio(audioBuffer),
            energyLevel: this.calculateEnergyLevel(audioBuffer),
            clippingDetected: this.detectClipping(audioBuffer),
            noiseLevel: this.calculateNoiseLevel(audioBuffer)
        };
        
        // Log quality issues
        if (analysis.silenceRatio > 0.8) {
            this.logEvent('AUDIO_QUALITY_WARNING', analysis, null, 'High silence ratio detected');
        }
        
        if (analysis.clippingDetected) {
            this.logEvent('AUDIO_QUALITY_WARNING', analysis, null, 'Audio clipping detected');
        }
        
        return analysis;
    }
    
    calculateSilenceRatio(audioBuffer) {
        try {
            const view = new DataView(audioBuffer);
            const numSamples = Math.floor(audioBuffer.byteLength / 2);
            let silentSamples = 0;
            const threshold = 100; // Silence threshold
            
            for (let i = 0; i < numSamples; i += 10) { // Sample every 10th sample
                const sample = Math.abs(view.getInt16(i * 2, true));
                if (sample < threshold) silentSamples++;
            }
            
            return silentSamples / (numSamples / 10);
        } catch (error) {
            console.warn('[VoiceCallDiagnostics] Error calculating silence ratio:', error);
            return 0;
        }
    }
    
    calculateEnergyLevel(audioBuffer) {
        try {
            const view = new DataView(audioBuffer);
            const numSamples = Math.floor(audioBuffer.byteLength / 2);
            let totalEnergy = 0;
            
            for (let i = 0; i < numSamples; i += 10) { // Sample every 10th sample
                const sample = view.getInt16(i * 2, true);
                totalEnergy += Math.abs(sample);
            }
            
            return totalEnergy / (numSamples / 10);
        } catch (error) {
            console.warn('[VoiceCallDiagnostics] Error calculating energy level:', error);
            return 0;
        }
    }
    
    detectClipping(audioBuffer) {
        try {
            const view = new DataView(audioBuffer);
            const numSamples = Math.floor(audioBuffer.byteLength / 2);
            const clippingThreshold = 32000; // Near max int16 value
            
            for (let i = 0; i < numSamples; i += 10) { // Sample every 10th sample
                const sample = Math.abs(view.getInt16(i * 2, true));
                if (sample > clippingThreshold) {
                    return true;
                }
            }
            
            return false;
        } catch (error) {
            console.warn('[VoiceCallDiagnostics] Error detecting clipping:', error);
            return false;
        }
    }
    
    calculateNoiseLevel(audioBuffer) {
        // Simple noise level calculation - could be enhanced
        return this.calculateEnergyLevel(audioBuffer);
    }
    
    // === WEBSOCKET MONITORING ===
    
    monitorWebSocketHealth(webSocket) {
        if (!webSocket) return null;
        
        const metrics = {
            readyState: webSocket.readyState,
            bufferedAmount: webSocket.bufferedAmount,
            protocol: webSocket.protocol,
            extensions: webSocket.extensions,
            timestamp: Date.now()
        };
        
        // Monitor buffer buildup
        if (metrics.bufferedAmount > 1024 * 1024) { // 1MB threshold
            this.logEvent('WEBSOCKET_WARNING', metrics, null, 'WebSocket buffer buildup detected');
        }
        
        return metrics;
    }
    
    // === PERFORMANCE MONITORING ===
    
    startPerformanceMonitoring() {
        // Monitor memory usage every 10 seconds
        this.memoryMonitorInterval = setInterval(() => {
            this.recordMemoryUsage();
        }, 10000);
        
        console.log('[VoiceCallDiagnostics] Started performance monitoring');
    }
    
    stopPerformanceMonitoring() {
        if (this.memoryMonitorInterval) {
            clearInterval(this.memoryMonitorInterval);
            this.memoryMonitorInterval = null;
        }
        
        console.log('[VoiceCallDiagnostics] Stopped performance monitoring');
    }
    
    recordMemoryUsage() {
        if (performance.memory) {
            const memoryInfo = {
                usedJSHeapSize: performance.memory.usedJSHeapSize,
                totalJSHeapSize: performance.memory.totalJSHeapSize,
                jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
                timestamp: Date.now()
            };
            
            this.metrics.memoryUsage.push(memoryInfo);
            
            // Keep only last 20 memory readings
            if (this.metrics.memoryUsage.length > 20) {
                this.metrics.memoryUsage.shift();
            }
            
            // Check for memory issues
            const usageRatio = memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit;
            if (usageRatio > 0.8) {
                this.logEvent('MEMORY_WARNING', memoryInfo, null, 'High memory usage detected');
            }
        }
    }
    
    // === METRICS UPDATES ===
    
    async updateMetrics(metricsUpdate) {
        // Merge with local metrics
        Object.assign(this.metrics, metricsUpdate);
        
        try {
            await fetch('/api/voice-call/metrics/update', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    metrics: { QualityMetrics: metricsUpdate }
                })
            });
        } catch (error) {
            console.error('[VoiceCallDiagnostics] Failed to update metrics:', error);
        }
    }
    
    // === SESSION MANAGEMENT ===
    
    async initializeSession(guestName = null, reservationId = null) {
        const clientDiagnostics = VoiceCallDiagnostics.collectClientDiagnostics();
        const networkQuality = await VoiceCallDiagnostics.assessNetworkQuality();
        
        try {
            const response = await fetch('/api/voice-call/session/start', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    property_id: this.propertyId,
                    user_id: this.userId,
                    client_diagnostics: clientDiagnostics,
                    network_quality: networkQuality,
                    guest_name: guestName,
                    reservation_id: reservationId
                })
            });
            
            if (response.ok) {
                console.log('[VoiceCallDiagnostics] Session initialized successfully');
                this.startPerformanceMonitoring();
                return true;
            } else {
                console.error('[VoiceCallDiagnostics] Failed to initialize session');
                return false;
            }
        } catch (error) {
            console.error('[VoiceCallDiagnostics] Error initializing session:', error);
            return false;
        }
    }
    
    async finalizeSession(endReason, finalMetrics = {}) {
        this.stopPerformanceMonitoring();
        
        // Calculate final metrics
        const sessionDuration = Date.now() - this.startTime;
        const enhancedFinalMetrics = {
            ...finalMetrics,
            sessionDuration,
            totalEvents: this.eventTimeline.length,
            totalErrors: this.errors.length,
            totalWarnings: this.warnings.length,
            averageMemoryUsage: this.calculateAverageMemoryUsage()
        };
        
        try {
            await fetch('/api/voice-call/session/end', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    end_reason: endReason,
                    final_metrics: enhancedFinalMetrics
                })
            });
            
            console.log(`[VoiceCallDiagnostics] Session finalized with reason: ${endReason}`);
        } catch (error) {
            console.error('[VoiceCallDiagnostics] Failed to finalize session:', error);
        }
    }
    
    calculateAverageMemoryUsage() {
        if (this.metrics.memoryUsage.length === 0) return 0;
        
        const totalUsage = this.metrics.memoryUsage.reduce((sum, usage) => sum + usage.usedJSHeapSize, 0);
        return totalUsage / this.metrics.memoryUsage.length;
    }
}

// Export for use in other modules
window.VoiceCallDiagnostics = VoiceCallDiagnostics;
